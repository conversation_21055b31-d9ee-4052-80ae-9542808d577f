{"compilerOptions": {"allowJs": true, "baseUrl": ".", "outDir": "dist", "target": "esnext", "useDefineForClassFields": true, "allowSyntheticDefaultImports": true, "composite": true, "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": false, "resolveJsonModule": true, "esModuleInterop": true, "skipLibCheck": true, "noImplicitAny": false, "lib": ["esnext", "dom"], "types": ["node", "vuetify"], "paths": {"@/*": ["src/*"], "crypto": ["node_modules/crypto-js"]}}, "include": ["src/**/*.js", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "vite.config.ts"], "exclude": ["node_modules", "*.d.ts"]}