<template>
  <v-row class="flex-d pa-10 fill-height">
    <v-col cols="12" lg="6" md="6" sm="12">
      <ValuesLeaderboardCard graph/>
    </v-col>
    <v-col cols="12" lg="6" md="6" sm="12">
      <ActivityFeedCard max-height="80vh"/>
    </v-col>
  </v-row>
</template>

<script lang="ts" setup>
import ValuesLeaderboardCard from "@/components/cards/ValuesLeaderboardCard.vue"
import ActivityFeedCard from "@/components/cards/ActivityFeedCard.vue";
</script>






