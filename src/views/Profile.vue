<template>
  <div class="pa-12">
    <v-row class="d-flex">
      <v-col md="4" sm="12" class="h-auto">
        <v-row class="w-100 mb-7" no-gutters>
          <ProfileInfoCard class="fill-height"/>
        </v-row>
        <v-row class="w-100" no-gutters>
          <Chart />
        </v-row>
      </v-col>

      <v-col md="4" sm="12">
        <ActivityCard
          :data-loaded="orbstarStore.received.length > 0"
          :orbstars="orbstarStore.received"
          cardName="Your Orbstars"
          max-height="77vh"
          @at-bottom="() => orbstarStore.fetchOlderReceivedOrbstars()"
        >
          <template #no-data>
            <OrbstarLoader v-if="orbstarStore.fetchingOrbstars" />
            <OrbstarAlien v-else>
              <v-row>
                <v-btn
                  class="text-none mx-auto rounded-pill"
                  color="primary"
                  @click="router.push({name: 'Dashboard'})"
                >
                  Give an Orbstar
                </v-btn>
              </v-row>
            </OrbstarAlien>
          </template>
        </ActivityCard>
      </v-col>

      <v-col md="4" sm="12">
        <ActivityCard
          :data-loaded="orbstarStore.given.length > 0"
          :orbstars="orbstarStore.given"
          cardName="What you've said"
          max-height="77vh"
          @at-bottom="() => orbstarStore.fetchOlderGivenOrbstars()"
        >
          <template #no-data>
            <OrbstarLoader v-if="orbstarStore.fetchingOrbstars" />
            <OrbstarUFO v-else>
              <v-row>
                <v-btn
                  class="text-none mx-auto rounded-pill"
                  color="primary"
                  @click="router.push({name: 'Dashboard'})"
                >
                  Give an Orbstar
                </v-btn>
              </v-row>
            </OrbstarUFO>
          </template>
        </ActivityCard>
      </v-col>
    </v-row>
  </div>
</template>

<script lang="ts" setup>
import ActivityCard from '@/components/cards/ActivityCard.vue';
import ProfileInfoCard from '@/components/cards/ProfileInfoCard.vue';
import Chart from '@/components/charts/Chart.vue';
import {useRouter} from "vue-router";
import OrbstarUFO from "@/components/placeholders/OrbstarUFO.vue";
import OrbstarAlien from "@/components/placeholders/OrbstarAlien.vue";
import {useOrbstarStore} from "@/store/orbstars";
import OrbstarLoader from "@/components/placeholders/OrbstarLoader.vue";

const router = useRouter();
const orbstarStore = useOrbstarStore();

orbstarStore.fetchOrbstars();
orbstarStore.fetchGivenOrbstars();
orbstarStore.fetchReceivedOrbstars();
</script>
