<template>
  <div class="justify-center align-center landing-page">
    <v-row class="h-50 justify-center ma-0">
      <v-img class="landing-page-img my-auto" src="@/assets/logo.svg"/>
    </v-row>
    <v-row class="h-50 justify-center ma-0">
      <v-row class="h-25 ma-0 pa-0">
        <v-row class="w-100 justify-center ma-0 pa-0">
          <h1 class="text-white landing-h1 text-center text-uppercase ma-0 pa-0">
            Stellar
          </h1>
        </v-row>
        <v-row class="w-100 justify-center mt-5">
          <h2 class="text-white landing-h2 text-center">
            Be lekker, share an Orbstar
          </h2>
        </v-row>
      </v-row>
      <v-row class="w-100 h-25">
        <div id="login-btn-container">
          <LoginButton
            id="login-btn"
            class="text-none"
            color="primary" height="60px" size="large" width="25%"
          >
            <span class="btn-content">Launch</span>
          </LoginButton>
        </div>
        <v-row class="w-100 d-inline-flex mt-5">
          <v-col cols="12" sm="3"/>
          <v-img
            v-for="(icon, i) in icons"
            :key="i"
            :src="icon"
            height="70px"
            width="70px"
            class="bottom-images"
          />
          <v-col cols="12" sm="3"/>
        </v-row>
        <div class="trapezoid"/>
        <div class="pa-1 bg-white easter-egg" @click="easterEggModal = true"/>
        <BaseDialog v-model="easterEggModal" width="40%" min-width="300px" height="80%">
          <template #title>The Lekker Ones</template>
          <template #body>
            <v-divider/>
            <v-chip-group class="mt-2">
              <v-chip v-for="h in hackers" :key="h">
                {{h}}
              </v-chip>
            </v-chip-group>
          </template>
        </BaseDialog>
      </v-row>
    </v-row>
  </div>
</template>

<script lang="ts" setup>
import "@/store/utils/subscriptions"
import LoginButton from '@/components/buttons/LoginButton.vue'
import {useAuthStore} from "@/store/auth";
import {useRouter} from "vue-router";

import {ref} from "vue";
import BaseDialog from "@/components/dialogs/BaseDialog.vue";

const easterEggModal = ref<boolean>(false);

const icons: string[] = [
  new URL("@/assets/values/self_development/icon.svg", import.meta.url).href,
  new URL("@/assets/values/reliability/icon.svg", import.meta.url).href,
  new URL("@/assets/values/impact/icon.svg", import.meta.url).href,
  new URL("@/assets/values/innovation/icon.svg", import.meta.url).href,
  new URL("@/assets/values/authenticity/icon.svg", import.meta.url).href,
  new URL("@/assets/values/initiative/icon.svg", import.meta.url).href
];
const hackers: string[] = [
  "Wendy Moulang",
  "Carel Burger",
  "Marcell Erasmus",
  "Steven Krog",
  "Nadia Venter",
  "Wilbur Salie",
  "Khomotso Zwane",
  "Franco Grobler",
  "Marichen Herholdt",
  "Tahlea Visser",
  "Aden Meyer",
  "Alandri Compion",
  "Anja Human",
  "Brent Pedersen",
  "Charl Jansen",
  "Dean Bellinghan",
  "Elzabé Cloete",
  "Greg van Eden",
  "Heindrich November",
  "Jason Adrense",
  "Leonie Slattery",
  "Elizabeth Ras",
  "Michael Mentoor",
  "Safia Hoossen",
  "Sonwabisa Notwala",
  "Tina Burton",
]

const authStore = useAuthStore();
const router = useRouter();

// Redirect if already authenticated
if (authStore.isAuthenticated) {
  router.push('/dashboard')
}
</script>

<style>
.landing-page-img {
  height: 30%;
  max-width: 80%;
  min-height: 200px;
  position: relative;
  z-index: 2;
}

.landing-page {
  background-image: linear-gradient(
    0deg, rgb(25, 110, 117, 0.85), rgb(6, 11, 50)
  ), url('@/assets/background.svg');
  background-size: cover;
  background-blend-mode: overlay;
  width: 100vw;
  height: 100vh;
}

.landing-h1 {
  font-family: Raleway, fantasy;
  letter-spacing: 1rem;
  font-weight: 700;
  font-size: 64px;
  line-height: 75px;
  height: 55px;
  z-index: 2;
  width: fit-content;
}

.landing-h2 {
  font-family: Raleway, fantasy;
  letter-spacing: 0.2rem;
  font-weight: 400;
  font-size: 43px;
  line-height: 50px;
  z-index: 2;
  max-width: fit-content;
}

#login-btn-container {
  display: block;
  width: 100%;
  height: fit-content;
}

#login-btn {
  display: block;
  margin: 10px auto;
  z-index: 2;
}

.btn-content {
  font-family: Montserrat, sans-serif;
  font-weight: 500;
  line-height: 27px;
  font-size: 27px;
}

.trapezoid {
  position: absolute;
  right: 10vw;
  bottom: 0;
  border-bottom: 68vh solid rgba(218, 210, 216, 0.25);
  border-left: 36vw solid transparent;
  border-right: 36vw solid transparent;
  height: 75vh;
  width: 80vw;
  z-index: 1;
}

.bottom-images {
  opacity: 0.5;
  z-index: 2;
}

.easter-egg {
  position: absolute;
  top: 1vw;
  right: 3vh;
  opacity: 0.5;
}
</style>
