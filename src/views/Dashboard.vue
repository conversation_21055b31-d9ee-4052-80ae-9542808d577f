<template>
  <div class="flex-d pa-10">
    <v-row>
      <v-col cols="12" lg="12" md="12" sm="12">
        <v-row>
          <v-col cols="12" lg="4" md="12" sm="12">
            <h1 class="text-primary mb-5">
              Welcome, <span v-if="userStore" class="text-primary">{{ userStore.firstName }}</span>
            </h1>
            <v-card class="rounded-xl overflow-y-auto" max-height="55vh" elevation="4">
              <v-col>
                <v-img class="pb-10" height="80" max-width="auto" src="@/assets/logo_orbstar.png"/>
                <v-col class="justify-center">
                  <FormOrbstar />
                </v-col>
              </v-col>
            </v-card>
          </v-col>
          <v-col cols="12" lg="5" md="12" sm="12">
            <ActivityFeedCard max-height="65vh"/>
          </v-col>
          <v-col cols="12" lg="3" md="12" sm="12">
            <ValuesLeaderboardCard/>
          </v-col>
        </v-row>
        <v-row class="my-0 mx-auto py-5 px-0 w-100 d-flex justify-space-between">
          <ValueIconCard
            v-for="value in valuesStore.values"
            :key="value.name" :value="value"
            class="value-icon-card"
          />
        </v-row>
      </v-col>
    </v-row>
  </div>
</template>

<script lang="ts" setup>
import ValuesLeaderboardCard from '@/components/cards/ValuesLeaderboardCard.vue'
import ValueIconCard from "@/components/cards/ValueIconCard.vue";
import ActivityFeedCard from "@/components/cards/ActivityFeedCard.vue";
import {useValuesStore} from "@/store/values";
import FormOrbstar from "@/components/forms/FormOrbstar.vue";
import {useUserStore} from "@/store/user";
import {useOrbstarStore} from "@/store/orbstars";

const userStore = useUserStore();
const valuesStore = useValuesStore();
const orbstarStore = useOrbstarStore();

orbstarStore.fetchOrbstars();
userStore.getProfile();
</script>

<style lang="scss" scoped>
.h-450 {
  height: 450px;
}

.value-icon-card {
  width: 15%;
  min-width: 220px;
  height: 150px;
}
</style>
