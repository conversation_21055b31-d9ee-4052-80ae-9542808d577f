/**
 * Simple test file for JWT utilities
 * Run this in the browser console to test JWT parsing
 */

import { parseJWT, getTokenExpiration, isTokenExpiringSoon, isTokenExpired } from './jwt';

// Test JWT token (this is a sample token, not a real one)
const sampleToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE5OTk5OTk5OTl9.Lp-38RDjiGu4EQEC9w6YLqmGw3OuJzKmKWE5bKw5pqE';

export function testJWTUtilities() {
  console.log('Testing JWT Utilities...');
  
  // Test parseJWT
  const payload = parseJWT(sampleToken);
  console.log('Parsed payload:', payload);
  
  // Test getTokenExpiration
  const expiration = getTokenExpiration(sampleToken);
  console.log('Token expiration:', new Date(expiration || 0));
  
  // Test isTokenExpired
  const expired = isTokenExpired(sampleToken);
  console.log('Is token expired:', expired);
  
  // Test isTokenExpiringSoon
  const expiringSoon = isTokenExpiringSoon(sampleToken, 1);
  console.log('Is token expiring soon (1 min):', expiringSoon);
  
  console.log('JWT Utilities test completed');
}

// Export for manual testing
(window as any).testJWTUtilities = testJWTUtilities;
