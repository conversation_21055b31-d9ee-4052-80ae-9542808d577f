/**
 * JWT Token utilities for parsing and validation
 */

export interface JWTPayload {
  sub: string;      // User ID
  typ: string;      // Token type
  iss: string;      // Issuer
  aud: string[];    // Audience
  exp: number;      // Expiration (Unix timestamp)
  iat: number;      // Issued at (Unix timestamp)
  nbf: number;      // Not before (Unix timestamp)
}

/**
 * Parse a JWT token and return its payload
 * @param token JWT token string
 * @returns Parsed payload or null if invalid
 */
export function parseJWT(token: string): JWTPayload | null {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Failed to parse JWT token:', error);
    return null;
  }
}

/**
 * Get token expiration time in milliseconds
 * @param token JWT token string
 * @returns Expiration time in milliseconds or null if invalid
 */
export function getTokenExpiration(token: string): number | null {
  const payload = parseJWT(token);
  return payload?.exp ? payload.exp * 1000 : null; // Convert to milliseconds
}

/**
 * Check if token is expiring soon
 * @param token JWT token string
 * @param bufferMinutes Minutes before expiration to consider "expiring soon"
 * @returns True if token is expiring soon or invalid
 */
export function isTokenExpiringSoon(token: string, bufferMinutes: number = 1): boolean {
  const expiration = getTokenExpiration(token);
  if (!expiration) return true;
  
  const now = Date.now();
  const bufferMs = bufferMinutes * 60 * 1000;
  return (expiration - now) <= bufferMs;
}

/**
 * Check if token is expired
 * @param token JWT token string
 * @returns True if token is expired or invalid
 */
export function isTokenExpired(token: string): boolean {
  const expiration = getTokenExpiration(token);
  if (!expiration) return true;
  
  return Date.now() >= expiration;
}

/**
 * Get time until token expires in milliseconds
 * @param token JWT token string
 * @returns Milliseconds until expiration or 0 if expired/invalid
 */
export function getTimeUntilExpiration(token: string): number {
  const expiration = getTokenExpiration(token);
  if (!expiration) return 0;
  
  const timeLeft = expiration - Date.now();
  return Math.max(0, timeLeft);
}
