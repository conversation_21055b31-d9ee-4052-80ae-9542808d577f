<template>
    <v-btn @click="login()">
      Log in
      <template v-for="slot in Object.keys($slots || {})" v-slot:[slot]>
        <slot :name="slot"/>
      </template>
    </v-btn>
</template>

<script setup lang="ts">
import {computed} from "vue";

const loginUrl = computed(() =>
  new URL('/login', import.meta.env.VITE_API_BASE_URL).href
);

function login() {
  window.location.href = loginUrl.value;
}
</script>

