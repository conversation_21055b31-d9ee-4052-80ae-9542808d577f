<template>
  <v-row class="w-100 align-center justify-center px-1">
    <v-img :src="value.icon" min-width="40" max-width="40" height="40" class="justify-center ma-auto"/>

    <div class="ml-2 flex-1-0 w-66">
      <v-row no-gutters>
        <span class="float-start text-capitalize">{{ value.name }}</span>
        <v-spacer/>
        <span class="float-end font-weight-bold">{{ numerator }}</span>
      </v-row>
      <v-row no-gutters class="mt-2 flex-nowrap">
        <div class="rounded-pill chart outer w-100">
          <div ref="barLine" class="rounded-pill chart inner"/>
        </div>
      </v-row>
    </div>

  </v-row>
</template>

<script lang="ts" setup>
import {Value} from "@/store/values/objects";
import {onMounted, ref, watch} from "vue";

const props = defineProps<{
  value: Value;
  numerator: number;
  denominator: number;
}>();

const barLine = ref<HTMLDivElement>();

function setBarLineWidth() {
  if (barLine.value) {
    barLine.value.style.width = '0';
    if (barLine.value.parentElement) {
      barLine.value.style.width =
        `${props.numerator / props.denominator * barLine.value.parentElement?.clientWidth - 7}px`
    }
  }
}

onMounted(() => {
  setBarLineWidth();
});

watch(() => props.numerator, () => setBarLineWidth());
watch(() => props.denominator, () => setBarLineWidth());
</script>

<style scoped lang="scss">
.chart{
  padding: 2px;

  &.outer {
    background-color: #9AD1D4;
  }
  &.inner {
    background-color: #197278;
    margin: 1px;
  }
}
</style>
