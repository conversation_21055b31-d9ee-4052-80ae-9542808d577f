<template>
  <v-card class="rounded-xl fill-height w-100" elevation="4">
    <v-row no-gutters>
      <v-col class="text-left" cols="12" lg="5">
        <v-card-title
          class="font-weight-black text-primary mt-3 ml-1"
          color="primary"
        >
          Values Matrix
        </v-card-title>
        <v-card-subtitle>
          {{ totalReceived }} Orbstars received
        </v-card-subtitle>
      </v-col>
      <v-col cols="12" lg="7">
        <!-- @vue-ignore -->
        <radar :data=" /*@ts-ignore*/ values" :options="options" class="ma-auto w-75 h-auto"/>
      </v-col>
    </v-row>
  </v-card>
</template>

<script lang="ts" setup>
import {computed, ref} from 'vue';
import {useValuesStore} from "@/store/values";
import {useOrbstarStore} from "@/store/orbstars";
import {Radar} from "vue-chartjs";
import {ChartData} from "chart.js";
import {
  Chart as ChartJS,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip
} from 'chart.js'

ChartJS.register(
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip
)

const orbstarStore = useOrbstarStore();
const valuesStore = useValuesStore();

const totalReceived = computed<number>(() => orbstarStore.received.length);

const dataComp = computed<number[]>(() => {
  const data = Array(valuesStore.values.length).fill(0);
  orbstarStore.received.forEach((o) => {
    const idx = valuesStore.valuesList.findIndex(x => x === o.value)
    if (idx >= 0) {
      data[idx] += 1;
    }
  })
  return data
})

const options = ref({
  responsive: true,
  plugins: {
    legend: {
      display: false,
    },
    title: {
      display: false,
    },
  },
});

const values = computed<ChartData>(() => ({
  labels: valuesStore.valuesList,
  datasets: [
    {
      data: dataComp.value,
      borderColor: ['#197278'],
      borderWidth: 3,
    },
  ],
}));
</script>

<style lang="scss" scoped>
.chart {
  height: 240px;
  margin: auto;
}
</style>
