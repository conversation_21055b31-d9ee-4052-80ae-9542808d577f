<template>
  <v-app-bar elevation="0" height="100">
    <v-container class="d-flex align-center app-bar" fluid height="60">
      <div style="width: 210px">
        <v-img
          class="ma-0 pa-0"
          height="70px"
          src="@/assets/logo_full.svg"
        />
      </div>
      <v-divider color="#FFF" inset thickness="3" vertical/>
      <v-tabs v-model="tab" align-tabs="title" class="text-white mx-0 px-0" color="white">
        <v-tab v-for="link in links" :key="link.value" :to="link.path" :value="link" color="white">
          {{ link.title }}
        </v-tab>
      </v-tabs>
      <v-spacer/>
      <ProfileAvatar/>
      <v-btn
        v-if="userStore.administrator"
        v-bind:href="reportingLink" class="ma-2" color="white"
        disabled
        icon="mdi-file-chart-outline"
      />
    </v-container>
  </v-app-bar>
</template>

<script lang="ts" setup>
import ProfileAvatar from '@/components/avatars/ProfileAvatar.vue';
import {ref} from "vue";
import {useUserStore} from "@/store/user";

const userStore = useUserStore();

const tab = ref();
const links = [
  {
    title: "Dashboard",
    path: "dashboard",
    value: 1,
  },
  {
    title: "My Profile",
    path: "profile",
    value: 2,
  },
  {
    title: "Values Feed",
    path: "feed",
    value: 3,
  },
];
const reportingLink = "https://www.aws.console.com";
</script>

<style scoped>
* {
  font-family: Lexend, serif;
}

.app-bar {
  background: linear-gradient(90deg, #04052E, #197278);
}
</style>
