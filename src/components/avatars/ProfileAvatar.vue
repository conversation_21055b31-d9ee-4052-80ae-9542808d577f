<template>
  <v-menu min-width="200px" rounded>
    <template v-slot:activator="{ props }">
      <v-btn icon v-bind="props">
        <PersonAvatar :person="userStore.toPerson"/>
      </v-btn>
    </template>
    <v-card class="rounded-xl">
      <v-card-text>
        <div class="mx-auto text-center">
          <PersonAvatar :person="userStore.toPerson"/>
          <h3>{{ userStore.firstName }}</h3>
          <p class="mt-1">
            {{ userStore.email }}
          </p>
          <v-divider class="my-3"></v-divider>
          <LogoutButton />
        </div>
      </v-card-text>
    </v-card>
  </v-menu>
</template>

<script setup lang="ts">
import LogoutButton from "@/components/buttons/LogoutButton.vue"
import {useUserStore} from "@/store/user";
import PersonAvatar from "@/components/avatars/PersonAvatar.vue";

const userStore = useUserStore();
</script>

