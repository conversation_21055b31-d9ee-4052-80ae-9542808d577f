<template>
  <v-avatar color="primary">
    <v-img v-if="imageURL" :alt="person.email" :src="imageURL"/>
    <template v-else>
      <span class="text-uppercase">
        <span ref="initialsText">{{ getInitials(person) }}</span>
      </span>
    </template>
  </v-avatar>
</template>

<script lang="ts" setup>
import {Person} from "@/components/objects";
import {getInitials} from "@/components/compostables";
import {onMounted, ref, useAttrs} from "vue";
import {useGravatarStore} from "@/store/gravatars";

const props = defineProps<{
  person: Person;
}>();

const gravitarStore = useGravatarStore();

const initialsText = ref<HTMLSpanElement>();

const attrs = useAttrs();

const imageURL = ref<string | null>(null);

onMounted(async () => {
  if (initialsText.value) {
    const fontSize = parseFloat(attrs.size as string) / 2;
    if (fontSize) {
      initialsText.value.style.fontSize = `${fontSize}px`;
    }
  }
  if (props.person.picture) {
    imageURL.value = props.person.picture;
  } else {
    imageURL.value = await gravitarStore.gravatar(props.person.email);
  }
});
</script>
