<template>
  <v-form :disabled="submitting">
    <v-autocomplete
      ref="receiverElement"
      v-model="receivers"
      :custom-filter="userFilter"
      :item-title="personName"
      :items="possibleReceivers"
      :loading="possibleReceivers.length === 0"
      chips
      closable-chips
      label="Who"
      multiple
      required return-object
    >
      <template #item="{ props, item }">
        <v-list-item
          :subtitle="item?.raw?.email"
          title="first_name"
          v-bind="props"
        >
          <template #title>
            {{ personName(item.raw) }}
          </template>
        </v-list-item>
      </template>
    </v-autocomplete>
    <v-select
      ref="valueElement"
      v-model="value"
      :items="values"
      label="What"
      required
    />
    <v-textarea
      ref="descriptionElement"
      v-model="description" clearable
      label="Description"
      required
      row-height="25"
      rows="3"
    />
    <v-row class="w-100" no-gutters>
      <v-spacer/>
      <v-btn
        :disabled="!canSubmit && !submitting"
        class="float-end"
        color="primary" rounded
        @click="kudus()"
      >
        Submit
      </v-btn>
    </v-row>

    <v-row>
      <v-alert
        v-if="alertMessage"
        :text="alertMessage"
        class="d-block"
        type="error"
      />
    </v-row>

  </v-form>
</template>

<script lang="ts" setup>
import {axiosApi} from "@/modules/axios/axiosAPI";
import {computed, ref} from "vue";
import {Person, User} from "@/components/objects";
import {useUserStore} from "@/store/user";
import {useValuesStore} from "@/store/values";
import {POLLING_TIMEOUT, useOrbstarStore} from "@/store/orbstars";
import {AxiosError} from "axios";

const userStore = useUserStore();
const valuesStore = useValuesStore();
const orbstarStore = useOrbstarStore();

const descriptionElement = ref();
const receiverElement = ref();
const valueElement = ref();

const alertMessage = ref<string>("");

const value = ref<string | null>(null);
const values = valuesStore.valuesList;

const receivers = ref<User[]>([]);
const possibleReceivers = ref<User[]>([]);

const description = ref("");

const submitting = ref<boolean>(false);
const canSubmit = computed(() =>
  value.value &&
  value.value.length > 0 &&
  receivers.value.length > 0 &&
  description.value.length > 0 &&
  !submitting.value
)

async function kudus() {
  if (canSubmit.value && !submitting.value) {
    submitting.value = true;
    orbstarStore.giveOrbstar(
      receivers.value.map(u => u.id),
      description.value,
      value.value as string
    ).catch((err: AxiosError) => {
      submitting.value = false;
      alertMessage.value = "An error occured while submitting the orbstar. Please try again."
      const data = err.response?.data;
      if (data) {
        const message = JSON.parse(data["Message"]);
        if (Object.hasOwn(message, 'receiver')) {
          receiverElement.value.errors = message['receiver']
        } else if (Object.hasOwn(message, 'description')) {
          receiverElement.value.errors = message['description']
        } else if (Object.hasOwn(message, 'value')) {
          receiverElement.value.errors = message['value']
        }
      }
    });
    value.value = "";
    receivers.value = [];
    description.value = "";
  }
  submitting.value = false;
}

function personName(person: Person) {
  if (!person.first_name && !person.last_name) {
    return person.email
  }
  return `${person.first_name} ${person.last_name}`.trim()
}

const userFilter = (_itemTitle: string, queryText: string, item?: Record<string, any>): boolean => {
  const user = item?.raw as Person;
  const firstName = user.first_name.toLowerCase();
  const lastName = user.last_name.toLowerCase();
  const email = user.last_name.toLowerCase();
  queryText = queryText.toLowerCase().trim();

  return firstName.indexOf(queryText) > -1 ||
    lastName.indexOf(queryText) > -1 ||
    email.indexOf(queryText) > -1;
}

let retryCount = 0;
function fetchUsers() {
  axiosApi.get<User[]>('users').then((res) => {
    if (res.status >= 300) {
      setTimeout(() => fetchUsers(), POLLING_TIMEOUT);
      return
    }
    const users = res.data
    const idx = users.findIndex(x => x.email === userStore.email);
    if (idx > -1) {
      users.splice(idx, 1)
    }
    possibleReceivers.value = users;
  }).catch(() => {
    retryCount = retryCount + 1;
    if (retryCount < 10) {
      setTimeout(() => fetchUsers(), POLLING_TIMEOUT);
    }
    return
  });
}

fetchUsers();
</script>

<style lang="scss" scoped>

</style>
