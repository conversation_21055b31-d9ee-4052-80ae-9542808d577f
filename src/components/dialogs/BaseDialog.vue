<template>
  <v-dialog v-model="model" v-bind="$attrs">
    <template v-for="slot in Object.keys($slots || {})" v-slot:[slot]="props">
      <slot :name="slot" v-bind="props"/>
    </template>
    <v-card>
      <v-row class="mx-2 mt-2 pa-2">
        <v-card-title v-if="$slots.title">
          <slot name="title"/>
        </v-card-title>
        <v-spacer/>
        <v-card-actions class="ma-0 pa-0 align-baseline">
          <v-btn
              class="ma-0 pa-0"
              icon="mdi-close"
              @click="model = false"
          />
        </v-card-actions>
      </v-row>
      <v-card-text v-if="$slots.body" class="mt-0 pt-0">
        <slot name="body"/>
      </v-card-text>
      <v-card-actions v-if="$slots.actions" class="ma-3 pa-2">
        <slot name="actions"/>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import {computed} from "vue";

const props = defineProps<{
  modelValue?: boolean;
}>();
const emit = defineEmits<{
  (event: "update:modelValue", value: boolean): void;
}>();

const model = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

</script>
