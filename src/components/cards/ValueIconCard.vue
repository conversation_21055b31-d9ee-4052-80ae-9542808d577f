<template>
  <v-card
      class="justify-center align-center fill-height ma-2 px-4 py-5 rounded-xl"
      elevation="3"
      @click="showDescription = !showDescription"
  >
    <v-img max-height="45px" :src="value.icon"/>
    <div class="value-name text-capitalize text-center mt-3">
      {{value.name}}
    </div>
    <BaseDialog v-model="showDescription" height="80%" width="80%">
      <template #body>
        <ValueDescriptionCard :value="value"/>
      </template>
    </BaseDialog>
  </v-card>
</template>

<script setup lang="ts">
import {Value} from "@/store/values/objects";
import {ref} from "vue";
import ValueDescriptionCard from "@/components/cards/ValueDescriptionCard.vue";
import BaseDialog from "@/components/dialogs/BaseDialog.vue";

defineProps<{
  value: Value
}>();

const showDescription = ref<boolean>(false);
</script>

<style scoped lang="scss">
.value-name {
  color: rgba(25, 114, 120, 1);
}
</style>
