<template>
  <ActivityCard
    :data-loaded="!orbstarStore.fetchingOrbstars"
    :orbstars="orbstarStore.orbstars"
    cardName="Activity Feed"
    @at-bottom="AtBottom"
  >
    <template #no-data>
      <OrbstarLoader v-if="orbstarStore.fetchingOrbstars" />
      <OrbstarUFO v-else/>
    </template>
  </ActivityCard>
</template>

<script setup lang="ts">
import ActivityCard from "@/components/cards/ActivityCard.vue";
import OrbstarUFO from "@/components/placeholders/OrbstarUFO.vue";
import {useOrbstarStore} from "@/store/orbstars";
import OrbstarLoader from "@/components/placeholders/OrbstarLoader.vue";

const orbstarStore = useOrbstarStore();

function AtBottom() {
  orbstarStore.fetchOlderOrbstars();
}

</script>
