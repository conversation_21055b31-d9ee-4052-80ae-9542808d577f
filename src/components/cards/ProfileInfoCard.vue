<template>
  <v-card class="rounded-xl h-100 pa-8 w-100" elevation="4">
    <v-row class="my-2">
      <v-col lg="3" md="4">
        <!--        <v-badge color="transparent" offset-x="15" offset-y="6">-->
        <!--          <template #badge>-->
        <!--            <v-btn class="text-primary" color="secondary" icon="mdi-pen" size="x-small"/>-->
        <!--          </template>-->
        <PersonAvatar :person="userStore.toPerson" class="mb-3" size="80"/>
        <!--        </v-badge>-->
      </v-col>
      <v-col class="text-left" lg="9" md="8">
        <v-card-title class="text-primary pl-0 pb-0 mb-0 font-weight-bold text-wrap">
          {{ userStore.firstName }} {{ userStore.lastName }}
        </v-card-title>
        <v-card-title v-if="userStore.department" class="text-secondary pl-0 py-0 my-0 font-weight-regular text-wrap">
          {{ userStore.department }}
        </v-card-title>
        <v-card-subtitle class="text-secondary pl-0">{{ userStore.email }}</v-card-subtitle>
      </v-col>
    </v-row>
    <ValueLineChart
      v-for="(item, i) in values"
      :key="i" :denominator="maxScore"
      :numerator="item.score"
      :value="item.value" class="my-2"
    />
  </v-card>
</template>

<script lang="ts" setup>
import {axiosApi} from "@/modules/axios/axiosAPI";
import {useUserStore} from "@/store/user";
import {ref} from "vue";
import {Value} from "@/store/values/objects";
import {useValuesStore} from "@/store/values";
import PersonAvatar from "@/components/avatars/PersonAvatar.vue";
import ValueLineChart from "@/components/charts/ValueLineChart.vue";
import {OrbstarsAssigned} from "@/components/objects";

const valuesStore = useValuesStore();
const userStore = useUserStore();
const values = ref<{ value: Value, score: number }[]>([]);
const maxScore = ref<number>(0);

for (const value of valuesStore.values) {
  values.value.push({value, score: 0});
}

axiosApi.get<OrbstarsAssigned>('orbstars/received').then((resp) => {
  for (const [key, value] of Object.entries(resp.data.value_stats)) {
    const idx = values.value.findIndex((x) => x.value.name === key);
    if (idx < 0) {
      continue;
    }
    values.value[idx].score = value;
    if (value > maxScore.value) {
      maxScore.value = value;
    }
  }
});
</script>
