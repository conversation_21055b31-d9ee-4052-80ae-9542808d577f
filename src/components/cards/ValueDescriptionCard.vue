<template>
  <v-card class="ma-0 pa-0 fill-height w-100">
    <v-row class="pa-0 ma-0">
      <v-col cols="12" md="4">
        <v-img max-height="95%" max-width="100%" :src="value.card"/>
      </v-col>
      <v-col cols="12" md="8">
        <v-card-text class="h-100">
          <div class="text-h5 text-uppercase font-weight-bold mt-0 mb-5">
            {{value.name}}
          </div>
          <div class="text-body-1 mt-0 mb-5">{{value.description}}</div>
          <div class="text-h6 text-none font-weight-bold mt-0 mb-5">Powers:</div>
          <div class="text-body-1">
            <v-list
              density="compact"
              lines="one"
              class="ma-0 pa-0"
            >
              <v-list-item
                v-for="(item, i) in value.powers"
                :key="i"
                :value="item"
                :title="item"
                class="ma-0 pa-0"
              >
                <template v-slot:prepend>
                  ⭐<div class="mx-3"/>
                </template>
              </v-list-item>
            </v-list>
          </div>
        </v-card-text>
      </v-col>
    </v-row>
  </v-card>
</template>

<script setup lang="ts">
import {Value} from "@/store/values/objects";

defineProps<{
  value: Value
}>();
</script>
