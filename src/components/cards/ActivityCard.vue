<template>
  <v-card class="rounded-xl fill-height pa-1" elevation="4" :loading="!dataLoaded">
    <v-card-title class="font-weight-black text-primary mt-3 ml-1">
      {{ cardName }}
    </v-card-title>
    <div v-if="orbstars.length === 0" class="w-100 fill-height align-center">
      <slot name="no-data">
          <div class="ma-auto text-center">
            Wees beter.
          </div>
      </slot>
    </div>
    <div v-else class="overflow-y-auto fill-height mb-2 py-2">
      <v-virtual-scroll :items="orbstars" height="90%" class="mb-3">
        <template v-slot="{ item, index }">
          <v-list-item :key="item.id" :value="item.id" :subtitle="timeSince(item)">
            <template #prepend>
              <PersonAvatar :person="item.giver"/>
            </template>

            <div>
              <span class="font-weight-bold">{{ youOrName(item.giver) }}</span>
              gave
              <span class="font-weight-bold">{{ youOrName(item.receiver) }}</span>
              an Orbstar for {{ item.value }}
              <v-card-text class="pa-0">{{ item.description }}</v-card-text>
            </div>

            <template v-if="userStore.administrator" #append>
              <v-btn icon="mdi-delete" @click="orbstarStore.deleteOrbstar(item.id)"/>
            </template>
          </v-list-item>
          <v-divider class="mx-2" />
          <div
            v-if="index === orbstars.length-3"
            v-intersect="onIntersect(index)"
          >
          </div>
        </template>
      </v-virtual-scroll>
    </div>
  </v-card>
</template>

<script lang="ts" setup>
import {Person} from "@/components/objects";
import {useUserStore} from "@/store/user";
import PersonAvatar from "@/components/avatars/PersonAvatar.vue";
import {Orbstar} from "@/store/orbstars/objects";
import {useOrbstarStore} from "@/store/orbstars";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(relativeTime)
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault(dayjs.tz.guess());

const userStore = useUserStore();
const orbstarStore = useOrbstarStore();
let emittedIndex: number;

defineProps<{
  cardName: string;
  orbstars: Orbstar[];
  dataLoaded: boolean;
}>();
const emit = defineEmits<{
  (event: 'at-bottom'): void
}>();

function timeSince(star: Orbstar): string {
  return dayjs.utc(star.created_at).fromNow()
}

function youOrName(person: Person): string {
  if (userStore.email === person.email) {
    return "You"
  }
  return `${person.first_name} ${person.last_name}`.trim() || person.email;
}

function onIntersect(itemIndex: number) {
  if (emittedIndex !== itemIndex) {
    emittedIndex = itemIndex;
    emit("at-bottom");
  }
}
</script>
