<template>
  <v-card :loading="!loaded" class="rounded-xl fill-height pr-8" elevation="4">
    <v-card-title class="font-weight-black text-primary mt-3 ml-1">
      Values Leaderboard
    </v-card-title>

    <template v-if="graph">
      <div class="d-flex align-start flex-column fill-height pb-16">
        <ValueLineChart
          v-for="(item, i) in values"
          :key="i" :denominator="maxScore"
          :numerator="item.score"
          :value="item.value" class="mx-2 my-auto py-4"
        />
      </div>
    </template>

    <v-list v-else class="bg-transparent" lines="one">
      <v-list-item v-for="value in values" :key="value.value.name">
        <v-row class="align-center justify-start">
          <v-col cols="2">
            <v-img :alt="value.value.name" :src="value.value.icon" height="40"/>
          </v-col>
          <v-col cols="7">
            <v-card-text>{{ value.value.name }}</v-card-text>
          </v-col>
          <v-col cols="3">
            <v-card-text class="font-weight-bold">{{ value.score }}</v-card-text>
          </v-col>
        </v-row>
      </v-list-item>
    </v-list>
  </v-card>
</template>

<script lang="ts" setup>
import {axiosApi} from "@/modules/axios/axiosAPI";
import {ref} from "vue";
import {Value} from "@/store/values/objects";
import {useValuesStore} from "@/store/values";
import ValueLineChart from "@/components/charts/ValueLineChart.vue";
import {POLLING_TIMEOUT} from "@/store/orbstars";
import {useAuthStore} from "@/store/auth";

defineProps<{
  graph?: boolean;
}>();

const authStore = useAuthStore();

const valuesStore = useValuesStore();
const values = ref<{ value: Value, score: number }[]>([])
const maxScore = ref<number>(0);
const loaded = ref<boolean>(false);

let requestFailures = 0;
let timeout: NodeJS.Timeout;

for (const value of valuesStore.values) {
  values.value.push({value, score: 0});
}

async function refreshLeaderboard() {
  loaded.value = false;
  clearTimeout(timeout);
  if (!authStore.authenticated) {
    return
  }
  await axiosApi.get<{ [key: string]: number }>('orbstars/leaderboard')
    .then((resp) => {
      maxScore.value = 0;
      for (const [key, value] of Object.entries(resp.data)) {
        const idx = values.value.findIndex((x) => x.value.name === key);
        if (idx < 0) {
          continue;
        }
        values.value[idx].score = value;
        maxScore.value += value;
      }
      loaded.value = true;
    })
    .catch(() => {
      requestFailures += 1;
      timeout = setTimeout(() => refreshLeaderboard(), POLLING_TIMEOUT*requestFailures);
      return;
    });
  timeout = setTimeout(() => refreshLeaderboard(), POLLING_TIMEOUT);
}

refreshLeaderboard();

</script>
