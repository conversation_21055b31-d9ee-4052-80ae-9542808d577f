import axios, {AxiosError, InternalAxiosRequestConfig} from "axios";
import {useAuthStore} from "@/store/auth";
import {POLLING_TIMEOUT} from "@/store/orbstars";

const axiosApi = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 60000,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  }
});

axiosApi.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore();
    if (authStore.authenticated) {
      const token = authStore.accessToken;
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  }
)

// Response interceptor for token refresh
axiosApi.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const authStore = useAuthStore();

    if (error.response?.status === 401 && authStore.refreshToken) {
      try {
        await authStore.refresh()
        // Retry the original request
        return axiosApi.request(error.config as InternalAxiosRequestConfig)
      } catch (refreshError) {
        authStore.logout()
        return Promise.reject(refreshError)
      }
    }

    if (error.response?.status === 403) {
      authStore.logout()
    }

    return Promise.reject(error)
  }
)

export { axiosApi }
