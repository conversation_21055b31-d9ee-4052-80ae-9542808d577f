import * as process from "process";
import {v5 as uuidV5, v4 as uuidV4} from "uuid";

const ListOfNulls = [
  null,
  undefined,
  'null',
  'undefined',
  "", ''
]

/**
 * Get an item from session storage.
 * @param name {string} The item name
 * @return If the value matches a null value, null.
 *          Otherwise, the JSON parse item
 */
export function fromSessionStorage(name: string): any {
  const value = sessionStorage.getItem(name);
  if (ListOfNulls.includes(value?.trim()) || !value) {
    return null
  }
  try {
    return JSON.parse(value)
  } catch (e) {
    if (e instanceof SyntaxError) {
      return JSON.parse(`"${value}"`)
    }
  }
}

/**
 * Set the item in session storage if the value is not null.
 * Remove the item in session storage if the value is null.
 * @param name {string} The name of the item in session storage.
 * @param value {any} The value in session storage.
 */
export function setOrRemoveSessionStorage(name: string, value: any) {
  if (!ListOfNulls.includes(value)) {
    sessionStorage.setItem(name, JSON.stringify(value))
  } else {
    sessionStorage.removeItem(name)
  }
}

/**
 * Get an item from local storage.
 * @param name {string} The item name
 * @return If the value matches a null value, null.
 *          Otherwise, the JSON parse item
 */
export function fromLocalStorage(name: string): any {
  const value = localStorage.getItem(name);
  if (ListOfNulls.includes(value?.trim()) || !value) {
    return null
  }
  try {
    return JSON.parse(value)
  } catch (e) {
    if (e instanceof SyntaxError) {
      return JSON.parse(`"${value}"`)
    }
  }
}

/**
 * Set the item in session storage if the value is not null.
 * Remove the item in session storage if the value is null.
 * @param name {string} The name of the item in session storage.
 * @param value {any} The value in session storage.
 */
export function setOrRemoveLocalStorage(name: string, value: any) {
  if (!ListOfNulls.includes(value)) {
    localStorage.setItem(name, JSON.stringify(value))
  } else {
    localStorage.removeItem(name)
  }
}

/**
 * Returns a recreatable uuid string when deployed in production.
 * @param devValue {string} The value that should be seen otherwise
 */
export function randomInProduction(devValue: string) {
  if (process.env.NODE_ENV === "production") {
    const sessionIDtag = "sessionID";
    let identifier = fromSessionStorage(sessionIDtag);
    if (!identifier) {
      identifier = uuidV4();
      setOrRemoveSessionStorage(sessionIDtag, identifier);
    }
    return uuidV5(devValue, identifier);
  }
  return devValue;
}
