import {setOrRemoveLocalStorage, setOrRemoveSessionStorage} from "@/store/utils/helpers";
import {useAuthStore, LocalStorageNames as AuthLocalStorageNames} from "@/store/auth";
import {useUserStore, StorageNames as UserSessionStorageNames} from "@/store/user";
import {useGravatarStore, StorageNames as GravitarStorageNames} from "@/store/gravatars";
import {useOrbstarStore, StorageNames as OrbstarStorageNames} from "@/store/orbstars";

useAuthStore().$subscribe((_mutation, state) => {
  for (const [key, value] of Object.entries(state)) {
    setOrRemoveLocalStorage(AuthLocalStorageNames[key], value);
  }
});

useGravatarStore().$subscribe((_mutation, state) => {
  for (const [key, value] of Object.entries(state)) {
    setOrRemoveSessionStorage(GravitarStorageNames[key], value);
  }
});

try {
  useOrbstarStore().$subscribe((_mutation, state) => {
    for (const [key, value] of Object.entries(state)) {
      setOrRemoveLocalStorage(OrbstarStorageNames[key], value);
    }
  });
} catch (err) {
  if (err instanceof ReferenceError) {
    console.log("Failed to subscribe to OrbstarStore.")
  }
}

useUserStore().$subscribe(async (_mutation, state) => {
  for (const [key, value] of Object.entries(state)) {
    setOrRemoveLocalStorage(UserSessionStorageNames[key], value);
  }
});
