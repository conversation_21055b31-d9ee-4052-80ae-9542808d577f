// Utilities
import {defineStore} from 'pinia';
import {ref, computed} from "vue";
import {md5} from "@/store/utils/md5";
import {fromLocalStorage} from "@/store/utils/helpers";

export const StorageNames = {
  gravatars: "gravatars"
}

export const useGravatarStore = defineStore('gravatars', () => {
  const gravatars = ref<{ [key: string]: string | null }>(fromLocalStorage(StorageNames.gravatars) || {});

  const gravatar = computed<(email: string) => Promise<string | null>>(
    () => async function (email: string): Promise<string | null> {
      if (Object.keys(gravatars.value).includes(email)) {
        return gravatars.value[email]
      } else {
        await getGravatar(email)
        return gravatars.value[email]
      }
    }
  )

  async function getGravatar(email: string) {
    fetch(
      `https://www.gravatar.com/avatar/${md5(email)}?d=404`
    ).then(r => {
        if (r.status < 400) {
          gravatars.value[email] = r.url
        } else {
          gravatars.value[email] = null;
        }
      }
    ).catch(() => {
      gravatars.value[email] = null;
    })
  }

  return {
    // state
    gravatars: gravatars,

    // getters
    gravatar: gravatar,

    // actions
    getGravitar: getGravatar
  }
})
