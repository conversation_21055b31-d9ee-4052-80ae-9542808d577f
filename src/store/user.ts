// Utilities
import { defineStore } from 'pinia';
import {
  fromLocalStorage,
  randomInProduction,
} from "@/store/utils/helpers";
import {Person, User} from "@/components/objects";
import {axiosApi} from "@/modules/axios/axiosAPI";

interface State {
  email: string;
  firstName: string;
  lastName: string;
  picture: string
  administrator: boolean;
  department: string;
  show: boolean;
}

export const StorageNames: { [key in keyof State]: string } = {
  email: randomInProduction("email"),
  firstName: randomInProduction("name"),
  lastName: randomInProduction("nickname"),
  picture: randomInProduction("picture"),
  administrator: randomInProduction("administrator"),
  department: randomInProduction("department"),
  show: randomInProduction("show"),
}

export const useUserStore = defineStore('user', {
  state: (): State => ({
    email: fromLocalStorage(StorageNames.email),
    firstName: fromLocalStorage(StorageNames.firstName),
    lastName: fromLocalStorage(StorageNames.lastName),
    picture: fromLocalStorage(StorageNames.picture),
    administrator: fromLocalStorage(StorageNames.administrator),
    department: fromLocalStorage(StorageNames.department),
    show: fromLocalStorage(StorageNames.show) || false,
  }),
  getters: {
    toPerson: (state): Person => <Person>{
      first_name: state.firstName,
      last_name: state.lastName,
      email: state.email,
      picture: state.picture,
    },
  },
  actions: {
    async getProfile() {
      const resp = await axiosApi.get<User>("user");
      this.$patch({
        firstName: resp.data.first_name,
        lastName: resp.data.last_name,
        email: resp.data.email,
        administrator: resp.data.admin,
        show: resp.data.show
      })
    },
    clear() {
      this.$patch({
        email: "",
        firstName: "",
        lastName: "",
        picture: "",
        administrator: false,
        department: "",
        show: false
      });
    }
  }
})
