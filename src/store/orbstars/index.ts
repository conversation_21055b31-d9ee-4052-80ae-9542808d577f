import {defineStore} from "pinia";
import {ref} from "vue";
import {Orbstar} from "@/store/orbstars/objects";
import {useUserStore} from "@/store/user";
import {axiosApi} from "@/modules/axios/axiosAPI";
import {filterNewOrbstars, sortOrbstars} from "@/store/orbstars/helpers";
import {Pagination} from "@/store/pagination";
import {fromLocalStorage} from "@/store/utils/helpers";
import {useAuthStore} from "@/store/auth";

export const POLLING_TIMEOUT = 60 * 1000;
export const PAGINATION_RESULTS_LENGTH = 15;

export const StorageNames = {
  orbstars: "orbstars",
  received: "orbstarsReceived",
  given: "orbstarsGiven",
}


export const useOrbstarStore = defineStore('orbstars', () => {
  const authStore = useAuthStore();
  const userStore = useUserStore();

  let pollingFunction: NodeJS.Timeout;
  // state
  const orbstars = ref<Orbstar[]>(fromLocalStorage(StorageNames.orbstars) || []);
  const received = ref<Orbstar[]>(fromLocalStorage(StorageNames.received) || []);
  const given = ref<Orbstar[]>(fromLocalStorage(StorageNames.given) || []);
  const fetchingOrbstars = ref<boolean>(false);

  // getters

  // actions
  async function fetchReceivedOrbstars(page: number = 1) {
    fetchingOrbstars.value = true;
    const resp = await axiosApi.get<{orbstars: Pagination<Orbstar>, value_stats: Record<string, string> }>(
      'orbstars/received', {params: {page: page}}
    )
    received.value.unshift(...filterNewOrbstars(resp.data.orbstars.items, received.value));
    fetchingOrbstars.value = false;
  }
  async function fetchGivenOrbstars(page: number = 1) {
    fetchingOrbstars.value = true;
    const resp = await axiosApi.get<{orbstars: Pagination<Orbstar>, value_stats: Record<string, string> }>(
      'orbstars/given', {params: {page: page}}
    )
    given.value.unshift(...filterNewOrbstars(resp.data.orbstars.items, given.value));
    fetchingOrbstars.value = false;
  }
  async function fetchOrbstars(page: number = 1) {
    clearTimeout(pollingFunction); // ensure only one polling function
    if (!authStore.authenticated) {
      return
    }

    fetchingOrbstars.value = true;
    const resp = await axiosApi.get<Pagination<Orbstar>>(
      'orbstars', {params: {page: page}}
    );
    const newReceived = resp.data.items.filter(x => x.receiver.email === userStore.email);
    const newGiven = resp.data.items.filter(x => x.giver.email === userStore.email);
    orbstars.value.unshift(...filterNewOrbstars(resp.data.items, orbstars.value));
    received.value.unshift(...filterNewOrbstars(newReceived, received.value));
    given.value.unshift(...filterNewOrbstars(newGiven, given.value));

    orbstars.value.sort(sortOrbstars);
    received.value.sort(sortOrbstars);
    given.value.sort(sortOrbstars);

    fetchingOrbstars.value = false;

    pollingFunction = setTimeout(() => fetchOrbstars(), POLLING_TIMEOUT);
  }
  async function fetchOlderOrbstars() {
    const page = Math.ceil(orbstars.value.length/PAGINATION_RESULTS_LENGTH) + 1;
    await fetchOrbstars(page);
  }
  async function fetchOlderReceivedOrbstars() {
    const page = Math.ceil(received.value.length/PAGINATION_RESULTS_LENGTH) + 1;
    await fetchReceivedOrbstars(page);
  }
  async function fetchOlderGivenOrbstars() {
    const page = Math.ceil(given.value.length/PAGINATION_RESULTS_LENGTH) + 1;
    await fetchGivenOrbstars(page);
  }

  async function giveOrbstar(receiver: number[], description: string, value: string) {
    const resp = await axiosApi.post<Orbstar[]>('orbstars', {
      "receiver": receiver,
      "description": description,
      "value": value
    })
    if (resp.status === 200) {
      orbstars.value.unshift(...resp.data)
    }
  }

  async function deleteOrbstar(id: number) {
    if (userStore.administrator) {
      const resp = await axiosApi.delete('orbstars', {params: {orbstar_id: id}});
      if (resp.status < 300) {
        orbstars.value.splice(
          orbstars.value.findIndex(x => x.id === id), 1
        );  // remove orbstar
      }
    }
  }

  return {
    // state
    orbstars,
    fetchingOrbstars,

    // getters
    received,
    given,

    //actions
    fetchOrbstars,
    fetchGivenOrbstars,
    fetchReceivedOrbstars,
    fetchOlderOrbstars,
    fetchOlderGivenOrbstars,
    fetchOlderReceivedOrbstars,
    giveOrbstar,
    deleteOrbstar,
  }
})
