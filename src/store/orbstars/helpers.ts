import {Orbstar} from "@/store/orbstars/objects";

export function sortOrbstars(a: Orbstar, b: Orbstar) {
  return Date.parse(a.created_at) < Date.parse(b.created_at) ? 1 : -1
}

export function filterNewOrbstars(newOrbstars: Orbstar[], existingOrbstars: Orbstar[]): Orbstar[] {
  const uniqueNew = newOrbstars.filter(
    (x: Orbstar) => existingOrbstars.filter(y => y.id === x.id).length === 0
  );
  uniqueNew.sort(sortOrbstars)
  return uniqueNew;
}
