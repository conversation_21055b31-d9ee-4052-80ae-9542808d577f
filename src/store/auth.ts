// Utilities
import { defineStore } from 'pinia'
import {
  fromLocalStorage,
  randomInProduction,
} from "@/store/utils/helpers";
import router from "@/router";
import axios from 'axios';

interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  show: boolean;
  admin: boolean;
}

interface State {
  accessToken: string|null;
  refreshToken: string|null;
  authenticated: boolean;
  user: User|null;
  isLoading: boolean;
}

export const LocalStorageNames: { [key in keyof State]: string } = {
  accessToken: randomInProduction("access_token"),
  refreshToken: randomInProduction("refresh_token"),
  authenticated: randomInProduction("authenticated"),
  user: randomInProduction("user"),
  isLoading: randomInProduction("isLoading")
}

export const useAuthStore = defineStore('auth', {
  state: (): State => ({
    accessToken: fromLocalStorage(LocalStorageNames.accessToken),
    refreshToken: fromLocalStorage(LocalStorageNames.refreshToken),
    authenticated: fromLocalStorage(LocalStorageNames.authenticated) || false,
    user: fromLocalStorage(LocalStorageNames.user),
    isLoading: false
  }),

  getters: {
    isAuthenticated: (state) => !!state.accessToken,
    isAdmin: (state) => state.user?.admin || false,
  },
  actions: {
    // Initialize auth from URL parameters (after Okta callback)
    initializeFromCallback() {
      const urlParams = new URLSearchParams(window.location.search)
      const accessToken = urlParams.get('access_token')
      const refreshToken = urlParams.get('refresh_token')

      if (accessToken && refreshToken) {
        this.setTokens(accessToken, refreshToken)
        // Clean URL
        window.history.replaceState({}, document.title, window.location.pathname)
        return true
      }
      return false
    },

    // Set tokens and store them
    setTokens(access: string, refresh: string) {
      this.$patch({
        accessToken: access,
        refreshToken: refresh,
        authenticated: true
      });
    },

    // Login - redirect to backend login endpoint
    login() {
      window.location.href = `${import.meta.env.VITE_API_BASE_URL}/login`
    },

    // Logout
    logout() {
      this.clear();
      router.push({name: 'Home'}).then();
    },

    // Clear all auth data
    clear() {
      this.$patch({
        authenticated: false,
        accessToken: null,
        refreshToken: null,
        user: null,
        isLoading: false
      });
    },

    // Refresh tokens
    async refresh() {
      if (!this.refreshToken) {
        throw new Error('No refresh token available')
      }

      try {
        const response = await axios.post<{
          access: string,
          refresh: string
        }>(`${import.meta.env.VITE_API_BASE_URL}/auth/refresh`, {
          "access": this.accessToken,
          "refresh": this.refreshToken
        });

        this.setTokens(response.data.access, response.data.refresh)
        return response.data
      } catch (error) {
        console.error('Token refresh failed:', error)
        this.logout()
        throw error
      }
    },

    // Fetch current user
    async fetchUser() {
      if (!this.isAuthenticated) return null

      try {
        this.isLoading = true
        const response = await axios.get<User>(`${import.meta.env.VITE_API_BASE_URL}/user`, {
          headers: {
            Authorization: `Bearer ${this.accessToken}`
          }
        })
        this.user = response.data
        return this.user
      } catch (error) {
        console.error('Failed to fetch user:', error)
        if ((error as any).response?.status === 401) {
          this.logout()
        }
        throw error
      } finally {
        this.isLoading = false
      }
    },
  }
})
