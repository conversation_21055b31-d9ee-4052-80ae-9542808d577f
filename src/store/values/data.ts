import {Value} from "@/store/values/objects";


export const values: Value[] = [
  {
    name: "Reliability",
    description: "There is a high level of trust and respect within our team which, coupled with a healthy level of accountability, leads to a feeling of safety and the recognition that all of us are committed to delivering our best. We feel proud of each other and are secure in the knowledge that we can rely on each other. We show up on time and are prepared to engage enthusiastically and with presence. We are consistent in our approach. We do what we say we’ll do and we don’t over commit ourselves or our teams.",
    powers: [
      "Accountability",
      "Punctuality",
      "Enthusiasm",
      "Consistency"
    ],
    icon: new URL("@/assets/values/reliability/icon.svg", import.meta.url).href,
    card: new URL("@/assets/values/reliability/card.webp", import.meta.url).href,
  }, {
    name: "Authenticity",
    description: "Byte Orbit is a space where originality and the confidence to speak our minds is encouraged. Unique ideas and opinions can be shared in an open and mutually respectful way. We are invited to be honest and true representations of ourselves. We listen with the intent to understand and reflect on what we’ve heard. We show compassion and empathy to others and respect each other’s differences.",
    powers: [
      "Originality",
      "Confidence",
      "Respect",
      "Honesty",
    ],
    icon: new URL("@/assets/values/authenticity/icon.svg", import.meta.url).href,
    card: new URL("@/assets/values/authenticity/card.webp", import.meta.url).href
  }, {
    name: "Self Development",
    description: "In such a dynamic and enterprising industry, there will always be room to develop and learn new skills or apply existing skills in new ways. Our culture is one of self-improvement, not just in terms of our careers, but also our own personal growth. This is actively encouraged, and it is widely understood that ‘nobody knows everything’. Sometimes the most satisfying and valuable lessons are learnt through making mistakes. ",
    powers: [
      "Self-empowerment",
      "Curiosity",
      "Commitment",
      "Discipline"
    ],
    icon: new URL("@/assets/values/self_development/icon.svg", import.meta.url).href,
    card: new URL("@/assets/values/self_development/card.webp", import.meta.url).href
  }, {
    name: "Innovation",
    description: "At Byte Orbit, non-conventional, creative and innovative solutions are actively sought out and this type of thinking is widely encouraged. Our industry is driven by people who dream big and use their imagination and ingenuity to conceptualise and build new ideas and products. Questioning and redefining concepts leave us open to changes and inventive outcomes. We are curious and always looking for ways to work smarter. We explore and experiment, we have the courage to fail and also celebrate our successes. ",
    powers: [
      "Creativity",
      "Problem-solving",
      "Ingenuity",
      "Venturesome"
    ],
    icon: new URL("@/assets/values/innovation/icon.svg", import.meta.url).href,
    card: new URL("@/assets/values/innovation/card.webp", import.meta.url).href
  }, {
    name: "Initiative",
    description: "We take initiative to identify issues and opportunities for improvement. We take accountability to follow through and see things through to completion.\n" +
      "\n" +
      "We don’t wait for someone else to do something that we could do ourselves. We don’t wait for instructions, we are brave and take the first step. We bring solutions when discussing problems and we are inclusive in our approach.",
    powers: [
      "Accountable",
      "Brave",
      "Innovative",
      "Helpful"
    ],
    icon: new URL("@/assets/values/initiative/icon.svg", import.meta.url).href,
    card: new URL("@/assets/values/initiative/card.webp", import.meta.url).href
  }, {
    name: "Impact",
    description: "We approach decision making and day-to-day interactions, understanding that our actions and behaviours impact each other. We treat each other with kindness and humanity. We ensure that our reactions and responses to situations and conflict are handled carefully. We don’t talk about each other unkindly.",
    powers: [
      "Kind",
      "Empathetic",
      "Humble",
      "Compassionate"
    ],
    icon: new URL("@/assets/values/impact/icon.svg", import.meta.url).href,
    card: new URL("@/assets/values/impact/card.webp", import.meta.url).href
  }
]
