import {defineStore} from "pinia";
import {values as valuesData} from "@/store/values/data";
import {computed, ref} from "vue";
import {Value} from "@/store/values/objects";

export const useValuesStore = defineStore('values', () => {
  // state
  const values = ref<Value[]>(valuesData);

  // getters
  const valuesList = computed<string[]>(() => {
    const vl: string[] = [];
    for (const v of Object.values(valuesData)) {
      vl.push(v.name);
    }
    vl.sort();
    return vl;
  });
  const valueByName = computed<Value>((name: string) => {
    const idx = valuesData.findIndex(x => x.name === name);
    return valuesData[idx];
  });

  // actions

  return {
    // state
    values,

    // getters
    valuesList,
    valueByName,

    //actions
  }
})
