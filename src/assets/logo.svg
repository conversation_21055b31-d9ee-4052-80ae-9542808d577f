<svg width="494" height="215" viewBox="0 0 494 215" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#filter0_i_445_6844)">
        <path d="M429.287 156.829C442.282 184.326 347.833 214.218 247.354 214.218C146.875 214.218 48.8816 183.33 65.4208 156.829C65.4208 130.743 146.875 109.596 247.354 109.596C347.833 109.596 429.287 130.743 429.287 156.829Z" fill="url(#paint0_linear_445_6844)"/>
    </g>
    <g filter="url(#filter1_d_445_6844)">
        <path d="M488.867 122.917C488.867 152.183 380.579 187.302 247 187.302C113.421 187.302 5.13306 152.183 5.13306 122.917C14.0305 92.1596 113.421 69.2424 247 69.2424C380.579 69.2424 475.951 92.1596 488.867 122.917Z" fill="url(#paint1_linear_445_6844)"/>
    </g>
    <g filter="url(#filter2_b_445_6844)">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M136.18 128.643C134.366 121.142 133.408 113.333 133.408 105.315C133.408 47.4981 183.207 0.628541 244.637 0.628542C306.067 0.628543 355.866 47.4981 355.866 105.315C355.866 113.387 354.895 121.247 353.057 128.795C322.654 141.325 285.249 148.714 244.802 148.714C204.193 148.714 166.649 141.265 136.18 128.643Z" fill="url(#paint2_linear_445_6844)"/>
    </g>
    <g opacity="0.4" filter="url(#filter3_i_445_6844)">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M267.146 12.9732C252.896 13.418 237.34 15.6918 221.393 19.9647C205.446 24.2377 190.837 30.0467 178.274 36.7865C187.761 25.635 202.066 16.5336 219.241 11.9318C236.415 7.32992 253.354 8.0593 267.146 12.9732Z" fill="#DAD2D8"/>
    </g>
    <defs>
        <filter id="filter0_i_445_6844" x="63.585" y="109.596" width="366.904" height="108.896" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="4.27415"/>
            <feGaussianBlur stdDeviation="2.13707"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_445_6844"/>
        </filter>
        <filter id="filter1_d_445_6844" x="0.858908" y="69.2424" width="492.282" height="126.608" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="4.27415"/>
            <feGaussianBlur stdDeviation="2.13707"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_445_6844"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_445_6844" result="shape"/>
        </filter>
        <filter id="filter2_b_445_6844" x="129.134" y="-3.64561" width="231.006" height="156.633" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="2.13707"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_445_6844"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_445_6844" result="shape"/>
        </filter>
        <filter id="filter3_i_445_6844" x="178.274" y="8.85327" width="88.8726" height="29.0018" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="4.27415"/>
            <feGaussianBlur stdDeviation="0.534269"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.890196 0 0 0 0 0.890196 0 0 0 0 0.890196 0 0 0 1 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_445_6844"/>
        </filter>
        <linearGradient id="paint0_linear_445_6844" x1="247.037" y1="109.596" x2="247.037" y2="214.218" gradientUnits="userSpaceOnUse">
            <stop stop-color="#04052E"/>
            <stop offset="1" stop-color="#197278"/>
        </linearGradient>
        <linearGradient id="paint1_linear_445_6844" x1="247" y1="69.2424" x2="247" y2="187.302" gradientUnits="userSpaceOnUse">
            <stop stop-color="#197278"/>
            <stop offset="1" stop-color="#9AD1D4"/>
        </linearGradient>
        <linearGradient id="paint2_linear_445_6844" x1="244.637" y1="0.628542" x2="244.637" y2="148.713" gradientUnits="userSpaceOnUse">
            <stop stop-color="#197278"/>
            <stop offset="1" stop-color="#9AD1D4"/>
        </linearGradient>
    </defs>
</svg>
