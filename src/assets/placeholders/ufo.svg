<svg width="149" height="118" viewBox="0 0 149 118" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g opacity="0.3">
        <g filter="url(#filter0_i_508_11269)">
            <path d="M127.774 33.3396C135.632 38.5919 115.234 61.6502 88.4539 77.7049C61.6734 93.7596 30.6199 101.185 30.7938 91.4786C26.6258 84.526 44.9567 65.8749 71.7372 49.8202C98.5177 33.7655 123.606 26.3869 127.774 33.3396Z" fill="url(#paint0_linear_508_11269)"/>
        </g>
        <g filter="url(#filter1_d_508_11269)">
            <path d="M138.236 14.781C142.912 22.5812 119.662 49.2439 84.0589 70.5874C48.4562 91.931 13.983 99.873 9.30685 92.0728C6.76387 82.4535 29.5924 60.4647 65.1952 39.1212C100.798 17.7776 129.879 8.64706 138.236 14.781Z" fill="url(#paint1_linear_508_11269)"/>
        </g>
        <g filter="url(#filter2_b_508_11269)">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M45.1496 72.6601C43.4675 70.9505 41.9646 69.0225 40.6833 66.8852C31.4453 51.4755 37.2292 31.0264 53.6021 21.2111C69.975 11.3957 90.7367 15.9308 99.9747 31.3405C101.265 33.4922 102.262 35.7421 102.978 38.0475C96.8765 46.2449 88.0875 54.191 77.3074 60.6536C66.4839 67.1422 55.2872 71.1557 45.1496 72.6601Z" fill="url(#paint2_linear_508_11269)"/>
        </g>
        <g opacity="0.4" filter="url(#filter3_i_508_11269)">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M61.5736 20.9053C57.8466 23.3007 54.0638 26.3923 50.4964 30.0791C46.9289 33.7659 43.9635 37.6482 41.6919 41.4519C42.4388 36.9639 44.7973 32.2526 48.6393 28.2821C52.4814 24.3115 57.1126 21.7994 61.5736 20.9053Z" fill="#DAD2D8"/>
        </g>
    </g>
    <defs>
        <filter id="filter0_i_508_11269" x="30.203" y="30.7227" width="99.2743" height="66.4256" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1.32821"/>
            <feGaussianBlur stdDeviation="0.664103"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_508_11269"/>
        </filter>
        <filter id="filter1_d_508_11269" x="7.78581" y="12.9067" width="132.375" height="84.7577" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1.32821"/>
            <feGaussianBlur stdDeviation="0.664103"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_508_11269"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_508_11269" result="shape"/>
        </filter>
        <filter id="filter2_b_508_11269" x="34.9557" y="14.6994" width="69.3503" height="59.289" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.664103"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_508_11269"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_508_11269" result="shape"/>
        </filter>
        <filter id="filter3_i_508_11269" x="41.6919" y="20.9053" width="19.8817" height="20.8787" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1.32821"/>
            <feGaussianBlur stdDeviation="0.166026"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.890196 0 0 0 0 0.890196 0 0 0 0 0.890196 0 0 0 1 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_508_11269"/>
        </filter>
        <linearGradient id="paint0_linear_508_11269" x1="71.6528" y1="49.8708" x2="88.3694" y2="77.7555" gradientUnits="userSpaceOnUse">
            <stop stop-color="#2C2C2C"/>
            <stop offset="1" stop-color="#797979"/>
        </linearGradient>
        <linearGradient id="paint1_linear_508_11269" x1="65.1952" y1="39.1212" x2="84.0589" y2="70.5874" gradientUnits="userSpaceOnUse">
            <stop stop-color="#777777"/>
            <stop offset="1" stop-color="#D5D5D5"/>
        </linearGradient>
        <linearGradient id="paint2_linear_508_11269" x1="53.6021" y1="21.2111" x2="77.2634" y2="60.6799" gradientUnits="userSpaceOnUse">
            <stop stop-color="#777777"/>
            <stop offset="1" stop-color="#D5D5D5"/>
        </linearGradient>
    </defs>
</svg>
