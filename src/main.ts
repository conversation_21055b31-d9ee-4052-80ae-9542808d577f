/**
 * main.ts
 *
 * Bootstraps Vuetify and other plugins then mounts the App`
 */

// Components
import App from './App.vue';

// Composables
import { createApp } from 'vue';

// Plugins
import { registerPlugins } from '@/plugins';

// Auth store initialization
import { useAuthStore } from '@/store/auth';

// Import subscriptions to ensure localStorage sync
import '@/store/utils/subscriptions';

const app = createApp(App);

registerPlugins(app);

app.mount('#app');

// Initialize auth store and start token renewal if authenticated
const authStore = useAuthStore();
if (authStore.isAuthenticated && authStore.accessToken) {
  // Start proactive token renewal
  authStore.scheduleTokenRenewal();
}
