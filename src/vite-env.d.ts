/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

interface ImportMetaEnv {
  readonly VITE_BACKEND_URL: string;
  readonly VITE_ADMIN_GROUP_NAME: string;
  // Sentry
  readonly VITE_SENTRY_DSN: string;
  readonly VITE_TRACE_PROPAGATION_TARGETS: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
