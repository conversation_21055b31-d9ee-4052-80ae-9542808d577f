/**
 * plugins/vuetify.ts
 *
 * Framework documentation: https://vuetifyjs.com`
 */

// Styles
import '@mdi/font/css/materialdesignicons.css'
import 'vuetify/styles'
import "@/styles/settings.scss"

// Composables
import { createVuetify } from 'vuetify'

// https://vuetifyjs.com/en/introduction/why-vuetify/#feature-guides
export default createVuetify({
  theme: {
    defaultTheme: 'light',
    themes: {
      light: {
        colors: {
          primary: '#FF784F',
          secondary: '#197278',
        },
      },
    },
  },
  defaults: {
    global: {
      elevation: 0
    },
    VSelect: {
      variant: "outlined"
    },
    VAutocomplete: {
      variant: "outlined"
    },
    VTextarea: {
      variant: "outlined"
    }
  }
})
