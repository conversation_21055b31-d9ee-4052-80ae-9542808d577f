/**
 * plugins/index.ts
 *
 * Automatically included in `./src/main.ts`
 */

// Plugins
import { loadFonts } from './webfontloader'
import vuetify from './vuetify'
import pinia from '../store'
import router from '../router'

// Types
import type { App } from 'vue'

// Sentry
import * as sentry from "@sentry/vue";

// polyfill TextEncoder for IE Edge
import { TextEncoder } from 'text-encoding';

if (typeof window.TextEncoder === 'undefined') {
  window.TextEncoder = TextEncoder
}

export function registerPlugins (app: App) {
  loadFonts().then();
  if (import.meta.env.SENTRY_DSN) {
    sentry.init({
      app,
      dsn: import.meta.env.SENTRY_DSN,
      integrations: [
        new sentry.BrowserTracing({
          routingInstrumentation: sentry.vueRouterInstrumentation(router),
        }),
        new sentry.Replay(),
      ],
      // Performance Monitoring
      tracesSampleRate: 1.0, // Capture 100% of the transactions, reduce in production!
      tracePropagationTargets: JSON.parse(import.meta.env.TRACE_PROPAGATION_TARGETS) as string[],
      // Session Replay
      replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
      replaysOnErrorSampleRate: 1.0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
    });
  }
  app
    .use(vuetify)
    .use(router)
    .use(pinia)
}
