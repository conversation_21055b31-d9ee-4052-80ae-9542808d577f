FROM node:lts-alpine as build

ARG MODE=qa

WORKDIR /app
COPY public ./public
COPY src ./src
COPY .browserslistrc .
COPY .env.development .
COPY .env.production .
COPY .env.qa .
COPY .eslintrc.js .
COPY index.html .
COPY package.json .
COPY tsconfig.json .
COPY tsconfig.node.json .
COPY vite.config.ts .
COPY yarn.lock .

RUN yarn
RUN yarn build --mode $MODE

FROM nginx as deploy

COPY --from=build /app/dist/ /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 3000