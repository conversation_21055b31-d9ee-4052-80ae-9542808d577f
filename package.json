{"name": "stellar", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build --force", "preview": "vite preview", "lint": "eslint . --fix --ignore-path .gitignore"}, "dependencies": {"@mdi/font": "7.2.96", "@sentry/vue": "^7.59.2", "@types/chart.js": "^2.9.37", "axios": "^1.4.0", "chart.js": "^4.3.2", "core-js": "^3.29.0", "crypto-js": "^4.1.1", "dayjs": "^1.11.9", "oidc-client-ts": "^2.2.4", "pinia": "^2.0.0", "roboto-fontface": "*", "text-encoding": "^0.7.0", "uuid": "^9.0.0", "vue": "^3.2.0", "vue-chartjs": "^5.2.0", "vue-router": "^4.0.0", "vuetify": "^3.3.10", "webfontloader": "^1.0.0"}, "devDependencies": {"@babel/types": "^7.21.4", "@types/crypto-js": "^4.1.1", "@types/node": "^18.16.20", "@types/uuid": "^9.0.2", "@types/webfontloader": "^1.6.35", "@vitejs/plugin-vue": "^4.0.0", "@vue/eslint-config-typescript": "^11.0.0", "eslint": "^8.46.0", "eslint-plugin-vue": "^9.16.1", "sass": "^1.64.2", "typescript": "^5.1.6", "vite": "^4.4.7", "vite-plugin-vuetify": "^1.0.0", "vue-tsc": "^1.8.8"}}